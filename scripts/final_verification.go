package main

import (
	"fmt"
	"log"
	"os"

	"golang.org/x/crypto/bcrypt"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// User 用户模型
type User struct {
	ID       uint   `gorm:"primaryKey;autoIncrement"`
	Phone    string `gorm:"type:varchar(20);uniqueIndex;not null"`
	Password string `gorm:"type:varchar(255);not null"`
	Role     string `gorm:"type:enum('admin','manager','user');default:'user'"`
	Nickname string `gorm:"type:varchar(100)"`
	Balance  int64  `gorm:"default:0"`
	IsActive int    `gorm:"type:tinyint;default:2"`
}

// TableName 指定表名
func (User) TableName() string {
	return "hook_user"
}

// 用户状态常量
const (
	UserStatusDisabled = 0 // 禁用
	UserStatusActive   = 1 // 正常
	UserStatusPending  = 2 // 审核中
)

func main() {
	// 获取数据库连接信息
	host := getEnv("DB_HOST", "***********")
	port := getEnv("DB_PORT", "3380")
	user := getEnv("DB_USER", "gmdns")
	password := getEnv("DB_PASSWORD", "Suyan15913..")
	dbname := getEnv("DB_NAME", "solve_web")

	// 构建DSN
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		user, password, host, port, dbname)

	// 连接数据库
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		log.Fatal("连接数据库失败:", err)
	}

	fmt.Println("🔍 用户注册审核功能最终验证")
	fmt.Println("================================")

	// 1. 验证数据库表结构
	fmt.Println("\n📋 步骤1: 验证数据库表结构...")
	
	var columnInfo struct {
		Field   string  `json:"field"`
		Type    string  `json:"type"`
		Null    string  `json:"null"`
		Key     string  `json:"key"`
		Default *string `json:"default"`
		Extra   string  `json:"extra"`
	}

	err = db.Raw("SHOW COLUMNS FROM hook_user WHERE Field = 'is_active'").Scan(&columnInfo).Error
	if err != nil {
		log.Fatal("查询字段信息失败:", err)
	}

	defaultVal := "NULL"
	if columnInfo.Default != nil {
		defaultVal = *columnInfo.Default
	}

	fmt.Printf("✅ is_active 字段信息:\n")
	fmt.Printf("   类型: %s\n", columnInfo.Type)
	fmt.Printf("   默认值: %s\n", defaultVal)

	if defaultVal != "2" {
		fmt.Printf("❌ 错误: 默认值应该是 2，但实际是 %s\n", defaultVal)
		return
	}

	// 2. 测试新用户注册（模拟代码逻辑）
	fmt.Println("\n📝 步骤2: 测试新用户注册...")
	
	testPhone := "13999999999"
	testNickname := "最终测试用户"
	
	// 删除可能存在的测试用户
	db.Where("phone = ?", testPhone).Delete(&User{})

	// 模拟 CreateUser 方法的逻辑
	hashedPassword, _ := bcrypt.GenerateFromPassword([]byte("123456"), bcrypt.DefaultCost)
	newUser := User{
		Phone:    testPhone,
		Password: string(hashedPassword),
		Role:     "user",
		Nickname: testNickname,
		Balance:  0,
		IsActive: UserStatusPending, // 明确设置为审核状态
	}

	err = db.Create(&newUser).Error
	if err != nil {
		log.Fatal("创建用户失败:", err)
	}

	fmt.Printf("✅ 用户创建成功:\n")
	fmt.Printf("   ID: %d\n", newUser.ID)
	fmt.Printf("   手机: %s\n", newUser.Phone)
	fmt.Printf("   昵称: %s\n", newUser.Nickname)
	fmt.Printf("   状态: %d (%s)\n", newUser.IsActive, getStatusName(newUser.IsActive))

	if newUser.IsActive != UserStatusPending {
		fmt.Printf("❌ 错误: 新用户状态应该是 %d (审核中)，但实际是 %d\n", UserStatusPending, newUser.IsActive)
		return
	}

	// 3. 测试不设置状态的情况（依赖数据库默认值）
	fmt.Println("\n🔧 步骤3: 测试数据库默认值...")
	
	testPhone2 := "13888888888"
	testNickname2 := "默认值测试用户"
	
	// 删除可能存在的测试用户
	db.Where("phone = ?", testPhone2).Delete(&User{})

	// 不设置 IsActive，让数据库使用默认值
	hashedPassword2, _ := bcrypt.GenerateFromPassword([]byte("123456"), bcrypt.DefaultCost)
	newUser2 := User{
		Phone:    testPhone2,
		Password: string(hashedPassword2),
		Role:     "user",
		Nickname: testNickname2,
		Balance:  0,
		// 不设置 IsActive，使用数据库默认值
	}

	err = db.Create(&newUser2).Error
	if err != nil {
		log.Fatal("创建用户2失败:", err)
	}

	fmt.Printf("✅ 用户2创建成功:\n")
	fmt.Printf("   ID: %d\n", newUser2.ID)
	fmt.Printf("   手机: %s\n", newUser2.Phone)
	fmt.Printf("   昵称: %s\n", newUser2.Nickname)
	fmt.Printf("   状态: %d (%s)\n", newUser2.IsActive, getStatusName(newUser2.IsActive))

	if newUser2.IsActive != UserStatusPending {
		fmt.Printf("❌ 错误: 数据库默认值应该是 %d (审核中)，但实际是 %d\n", UserStatusPending, newUser2.IsActive)
	}

	// 4. 验证待审核用户列表
	fmt.Println("\n📋 步骤4: 验证待审核用户列表...")
	
	var pendingUsers []User
	err = db.Where("is_active = ?", UserStatusPending).Find(&pendingUsers).Error
	if err != nil {
		log.Fatal("查询待审核用户失败:", err)
	}

	fmt.Printf("✅ 待审核用户数量: %d\n", len(pendingUsers))
	for _, user := range pendingUsers {
		fmt.Printf("   - ID: %d, 昵称: %s, 手机: %s\n", user.ID, user.Nickname, user.Phone)
	}

	// 5. 测试审核功能
	fmt.Println("\n✅ 步骤5: 测试审核功能...")
	
	// 审核通过第一个用户
	err = db.Model(&User{}).Where("id = ?", newUser.ID).Update("is_active", UserStatusActive).Error
	if err != nil {
		log.Fatal("审核用户失败:", err)
	}

	// 审核拒绝第二个用户
	err = db.Model(&User{}).Where("id = ?", newUser2.ID).Update("is_active", UserStatusDisabled).Error
	if err != nil {
		log.Fatal("拒绝用户失败:", err)
	}

	fmt.Printf("✅ 审核操作完成:\n")
	fmt.Printf("   用户1 (%s): 审核通过 → 正常\n", newUser.Nickname)
	fmt.Printf("   用户2 (%s): 审核拒绝 → 禁用\n", newUser2.Nickname)

	// 6. 最终状态统计
	fmt.Println("\n📊 步骤6: 最终状态统计...")
	
	var stats []struct {
		IsActive int   `json:"is_active"`
		Count    int64 `json:"count"`
	}
	
	err = db.Model(&User{}).
		Select("is_active, COUNT(*) as count").
		Group("is_active").
		Order("is_active").
		Scan(&stats).Error
	if err != nil {
		log.Fatal("查询统计信息失败:", err)
	}

	fmt.Println("用户状态分布:")
	for _, stat := range stats {
		statusName := getStatusName(stat.IsActive)
		fmt.Printf("  状态 %d (%s): %d 个用户\n", stat.IsActive, statusName, stat.Count)
	}

	// 7. 清理测试数据
	fmt.Println("\n🧹 步骤7: 清理测试数据...")
	
	db.Where("phone IN ?", []string{testPhone, testPhone2}).Delete(&User{})
	fmt.Printf("✅ 测试用户已清理\n")

	// 8. 最终验证结果
	fmt.Println("\n🎉 最终验证结果:")
	fmt.Println("✅ 数据库字段默认值正确 (2)")
	fmt.Println("✅ 代码明确设置状态正确")
	fmt.Println("✅ 数据库默认值生效")
	fmt.Println("✅ 待审核用户查询正常")
	fmt.Println("✅ 审核功能正常")
	fmt.Println("\n🚀 用户注册审核功能验证通过！")
}

// getStatusName 获取状态名称
func getStatusName(status int) string {
	switch status {
	case UserStatusDisabled:
		return "禁用"
	case UserStatusActive:
		return "正常"
	case UserStatusPending:
		return "审核中"
	default:
		return "未知"
	}
}

// getEnv 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
