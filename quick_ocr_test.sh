#!/bin/bash

# 快速OCR深度测试脚本

echo "🔍 [OCR-TEST] 快速OCR深度测试"
echo "🔍 [OCR-TEST] 此测试将验证OCR模型调用和日志输出功能"
echo ""

# 检查编译后的程序是否存在
if [ ! -f "solve-api" ]; then
    echo "❌ [OCR-TEST] solve-api程序不存在，正在编译..."
    go build -o solve-api main.go
    if [ $? -ne 0 ]; then
        echo "❌ [OCR-TEST] 编译失败"
        exit 1
    fi
    echo "✅ [OCR-TEST] 编译成功"
fi

# 清理旧的日志文件
echo "🔍 [OCR-TEST] 清理旧的OCR日志文件..."
rm -f logs/ocr_response_*.log

# 启动服务（后台运行）
echo "🔍 [OCR-TEST] 启动服务..."
./solve-api > service_output.log 2>&1 &
SERVICE_PID=$!

# 等待服务启动
sleep 3

# 发送测试请求
echo "🔍 [OCR-TEST] 发送测试请求..."
curl -X POST http://localhost:8080/api/v1/solve/question \
  -H "Content-Type: application/json" \
  -d '{
    "app_id": "PvpKuwyCBxUmnvBG",
    "app_secret": "FNKdsTcZSHrKJIMCtK3sDbfOQHBGnbFN",
    "image_url": "https://example.com/test-image.jpg"
  }' \
  -s -o response.json

# 等待处理完成
sleep 5

# 检查服务是否还在运行
if kill -0 $SERVICE_PID 2>/dev/null; then
    echo "⚠️  [OCR-TEST] 服务仍在运行，手动停止..."
    kill $SERVICE_PID
else
    echo "✅ [OCR-TEST] 服务已自动停止（符合预期）"
fi

# 检查结果
echo ""
echo "🔍 [OCR-TEST] 测试结果分析："

# 检查服务输出
if [ -f "service_output.log" ]; then
    echo "📝 [OCR-TEST] 服务输出日志："
    echo "----------------------------------------"
    tail -20 service_output.log
    echo "----------------------------------------"
fi

# 检查OCR响应日志
if ls logs/ocr_response_*.log 1> /dev/null 2>&1; then
    echo "✅ [OCR-TEST] 发现OCR响应日志文件："
    ls -la logs/ocr_response_*.log
    
    LATEST_LOG=$(ls -t logs/ocr_response_*.log | head -1)
    echo ""
    echo "📄 [OCR-TEST] 最新日志文件内容预览："
    echo "----------------------------------------"
    head -30 "$LATEST_LOG"
    echo "----------------------------------------"
else
    echo "❌ [OCR-TEST] 未找到OCR响应日志文件"
fi

# 检查API响应
if [ -f "response.json" ]; then
    echo ""
    echo "📡 [OCR-TEST] API响应："
    echo "----------------------------------------"
    cat response.json
    echo ""
    echo "----------------------------------------"
fi

echo ""
echo "🔍 [OCR-TEST] 测试完成"
echo "🔍 [OCR-TEST] 如果看到OCR响应日志文件，说明修改成功"
