#!/bin/bash

# OCR深度测试脚本
# 用于测试OCR模型调用和原始响应数据输出

echo "🔍 [OCR-TEST] ==================== OCR深度测试开始 ===================="
echo "🔍 [OCR-TEST] 此测试将调用OCR模型并输出原始响应数据到日志文件"
echo "🔍 [OCR-TEST] 测试完成后进程将自动中断，请查看logs目录下的日志文件"
echo ""

# 检查是否有现有的服务进程
echo "🔍 [OCR-TEST] 检查现有服务进程..."
if pgrep -f "solve-api" > /dev/null; then
    echo "⚠️  [OCR-TEST] 发现现有服务进程，正在停止..."
    pkill -f "solve-api"
    sleep 2
fi

# 启动服务
echo "🔍 [OCR-TEST] 启动测试服务..."
./solve-api &
SERVICE_PID=$!

# 等待服务启动
echo "🔍 [OCR-TEST] 等待服务启动..."
sleep 3

# 检查服务是否启动成功
if ! curl -s http://localhost:8080/health > /dev/null 2>&1; then
    echo "❌ [OCR-TEST] 服务启动失败，请检查配置"
    kill $SERVICE_PID 2>/dev/null
    exit 1
fi

echo "✅ [OCR-TEST] 服务启动成功"

# 准备测试数据
APP_ID="PvpKuwyCBxUmnvBG"  # 使用已知的测试应用ID
APP_SECRET="FNKdsTcZSHrKJIMCtK3sDbfOQHBGnbFN"  # 使用已知的测试应用密钥
IMAGE_URL="https://example.com/test-image.jpg"  # 测试图片URL

echo "🔍 [OCR-TEST] 测试参数:"
echo "🔍 [OCR-TEST] - APP_ID: $APP_ID"
echo "🔍 [OCR-TEST] - APP_SECRET: $APP_SECRET"
echo "🔍 [OCR-TEST] - IMAGE_URL: $IMAGE_URL"
echo ""

# 发送测试请求
echo "🔍 [OCR-TEST] 发送OCR测试请求..."
echo "🔍 [OCR-TEST] 请求将触发OCR模型调用，原始响应数据将输出到日志文件"
echo "🔍 [OCR-TEST] 进程将在OCR处理完成后自动中断"
echo ""

curl -X POST http://localhost:8080/api/v1/solve/question \
  -H "Content-Type: application/json" \
  -d "{
    \"app_id\": \"$APP_ID\",
    \"app_secret\": \"$APP_SECRET\",
    \"image_url\": \"$IMAGE_URL\"
  }" \
  -w "\n🔍 [OCR-TEST] HTTP状态码: %{http_code}\n" \
  -s

echo ""
echo "🔍 [OCR-TEST] 请求已发送，等待OCR处理完成..."
echo "🔍 [OCR-TEST] 如果一切正常，服务进程应该会在OCR处理完成后自动中断"
echo "🔍 [OCR-TEST] 请查看控制台输出和logs目录下的OCR响应日志文件"

# 等待一段时间让请求处理完成
sleep 10

# 检查服务是否还在运行
if pgrep -f "solve-api" > /dev/null; then
    echo "⚠️  [OCR-TEST] 服务进程仍在运行，可能OCR处理未完成或出现错误"
    echo "🔍 [OCR-TEST] 手动停止服务进程..."
    pkill -f "solve-api"
else
    echo "✅ [OCR-TEST] 服务进程已自动中断，OCR深度测试完成"
fi

echo ""
echo "🔍 [OCR-TEST] ==================== OCR深度测试结束 ===================="
echo "🔍 [OCR-TEST] 请检查以下内容："
echo "🔍 [OCR-TEST] 1. 控制台输出中的OCR测试日志"
echo "🔍 [OCR-TEST] 2. logs目录下的OCR响应日志文件"
echo "🔍 [OCR-TEST] 3. 服务日志中的详细调试信息"
echo ""

# 显示logs目录内容
if [ -d "logs" ]; then
    echo "🔍 [OCR-TEST] logs目录内容:"
    ls -la logs/
    echo ""
    
    # 显示最新的OCR响应日志文件
    LATEST_OCR_LOG=$(ls -t logs/ocr_response_*.log 2>/dev/null | head -1)
    if [ -n "$LATEST_OCR_LOG" ]; then
        echo "🔍 [OCR-TEST] 最新的OCR响应日志文件: $LATEST_OCR_LOG"
        echo "🔍 [OCR-TEST] 文件大小: $(wc -c < "$LATEST_OCR_LOG") 字节"
        echo "🔍 [OCR-TEST] 文件内容预览:"
        echo "----------------------------------------"
        head -20 "$LATEST_OCR_LOG"
        echo "----------------------------------------"
    else
        echo "⚠️  [OCR-TEST] 未找到OCR响应日志文件，可能OCR调用未成功"
    fi
else
    echo "⚠️  [OCR-TEST] logs目录不存在，可能OCR调用未成功"
fi
