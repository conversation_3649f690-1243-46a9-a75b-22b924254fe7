package services

import (
	"encoding/json"
	"fmt"
	"log"
	"solve-go-api/internal/config"
	"solve-go-api/internal/models"
	"time"

	"gorm.io/gorm"
)

// SolveService 核心解题服务
type SolveService struct {
	db              *gorm.DB
	cfg             *config.Config
	modelService    *ModelService
	parserService   *ParserService
	cacheService    *CacheService
	questionService *QuestionService
	matchService    *MatchService
	appService      *AppService
	logService      *LogService
}

// NewSolveService 创建解题服务
func NewSolveService(
	db *gorm.DB,
	cfg *config.Config,
	modelService *ModelService,
	parserService *ParserService,
	cacheService *CacheService,
	questionService *QuestionService,
	matchService *MatchService,
	appService *AppService,
	logService *LogService,
) *SolveService {
	return &SolveService{
		db:              db,
		cfg:             cfg,
		modelService:    modelService,
		parserService:   parserService,
		cacheService:    cacheService,
		questionService: questionService,
		matchService:    matchService,
		appService:      appService,
		logService:      logService,
	}
}

// ProcessSolveRequest 处理解题请求
func (s *SolveService) ProcessSolveRequest(request *models.APIRequest) *models.ProcessContext {
	ctx := &models.ProcessContext{
		AppID:     request.AppID,
		UserURL:   request.ImageURL,
		StartTime: time.Now(),
		Status:    0,
	}

	log.Printf("🚀 [SOLVE] 开始处理解题请求 - AppID: %s, ImageURL: %s", request.AppID, request.ImageURL)

	// 1. 验证应用和用户
	log.Printf("📋 [SOLVE] 步骤1: 验证应用和用户")
	if err := s.validateAppAndUser(ctx); err != nil {
		log.Printf("❌ [SOLVE] 应用/用户验证失败: %v", err)
		ctx.SetError(err.Error())
		return ctx
	}
	log.Printf("✅ [SOLVE] 应用/用户验证成功 - UserID: %d, Balance: %d", ctx.User.ID, ctx.User.Balance)

	// 2. 检查用户积分
	log.Printf("💰 [SOLVE] 步骤2: 检查用户积分 - 当前积分: %d", ctx.User.Balance)
	if ctx.User.Balance <= 0 {
		log.Printf("❌ [SOLVE] 用户积分不足: %d", ctx.User.Balance)
		ctx.SetError("用户积分不足")
		return ctx
	}

	// 3. 调用OCR模型识别图片
	log.Printf("🔍 [SOLVE] 步骤3: 调用OCR模型识别图片")
	if err := s.processOCR(ctx); err != nil {
		log.Printf("❌ [SOLVE] OCR处理失败: %v", err)
		ctx.SetError(err.Error())
		return ctx
	}
	log.Printf("✅ [SOLVE] OCR处理成功 - Token消耗: %d, 题目类型: %s", ctx.OCRToken, ctx.SaveQuestion.Type)

	// 4. 检查Redis缓存
	log.Printf("🔄 [SOLVE] 步骤4: 检查Redis缓存")
	if s.checkRedisCache(ctx) {
		log.Printf("✅ [SOLVE] Redis缓存命中")
		return ctx
	}
	log.Printf("⚪ [SOLVE] Redis缓存未命中")

	// 5. 检查MySQL精确匹配
	log.Printf("🎯 [SOLVE] 步骤5: 检查MySQL精确匹配")
	if s.checkMySQLExactMatch(ctx) {
		log.Printf("✅ [SOLVE] MySQL精确匹配成功")
		return ctx
	}
	log.Printf("⚪ [SOLVE] MySQL精确匹配未命中")

	// 6. 检查MySQL模糊匹配
	log.Printf("🔍 [SOLVE] 步骤6: 检查MySQL模糊匹配")
	if s.checkMySQLFuzzyMatch(ctx) {
		log.Printf("✅ [SOLVE] MySQL模糊匹配成功")
		return ctx
	}
	log.Printf("⚪ [SOLVE] MySQL模糊匹配未命中")

	// 7. 调用Solve模型解答
	log.Printf("🤖 [SOLVE] 步骤7: 调用Solve模型解答")
	if err := s.processSolve(ctx); err != nil {
		log.Printf("❌ [SOLVE] Solve模型处理失败: %v", err)
		ctx.SetError(err.Error())
		return ctx
	}
	log.Printf("✅ [SOLVE] Solve模型处理成功")

	// 8. 保存题目到数据库并回写缓存
	log.Printf("💾 [SOLVE] 步骤8: 保存题目到数据库并回写缓存")
	if err := s.saveAndCache(ctx); err != nil {
		log.Printf("❌ [SOLVE] 保存和缓存失败: %v", err)
		ctx.SetError(err.Error())
		return ctx
	}
	log.Printf("✅ [SOLVE] 保存和缓存成功")

	log.Printf("🎉 [SOLVE] 解题请求处理完成 - 总耗时: %v", time.Since(ctx.StartTime))
	return ctx
}

// validateAppAndUser 验证应用和用户
func (s *SolveService) validateAppAndUser(ctx *models.ProcessContext) error {
	app, err := s.appService.GetAppByID(ctx.AppID)
	if err != nil {
		return fmt.Errorf("应用不存在")
	}

	if app.Status != 0 {
		return fmt.Errorf("应用已被禁用")
	}

	user, err := s.appService.GetUserByID(app.UserID)
	if err != nil {
		return fmt.Errorf("用户不存在")
	}

	// 检查用户状态
	if user.IsActive != models.UserStatusActive {
		switch user.IsActive {
		case models.UserStatusDisabled:
			return fmt.Errorf("用户已被禁用")
		case models.UserStatusPending:
			return fmt.Errorf("账户正在审核中，请等待管理员审核")
		default:
			return fmt.Errorf("账户状态异常")
		}
	}

	ctx.App = app
	ctx.User = user
	return nil
}

// processOCR 处理OCR识别
func (s *SolveService) processOCR(ctx *models.ProcessContext) error {
	log.Printf("🔍 [OCR] 开始OCR处理 - 模型: %s, 图片URL: %s", s.cfg.Models.OCRModel, ctx.UserURL)

	// 调用OCR模型
	response, err := s.modelService.CallOCRModel(s.cfg.Models.OCRModel, ctx.UserURL)
	if err != nil {
		log.Printf("❌ [OCR] 模型调用失败: %v", err)
		return fmt.Errorf("OCR模型调用失败: %w", err)
	}
	log.Printf("✅ [OCR] 模型调用成功 - Token消耗: %d", response.Usage.TotalTokens)

	// 记录token消耗
	ctx.OCRToken = response.Usage.TotalTokens

	// 解析OCR响应
	log.Printf("📝 [OCR] 开始解析OCR响应")
	saveQuestion, err := s.parserService.ParseOCRResponse(response, ctx.UserURL)
	if err != nil {
		log.Printf("❌ [OCR] 响应解析失败: %v", err)
		return err
	}
	log.Printf("✅ [OCR] 响应解析成功 - 题目类型: %s, 内容长度: %d", saveQuestion.Type, len(saveQuestion.Content))

	ctx.SaveQuestion = saveQuestion

	// 🔍 [OCR-TEST] OCR处理完成，输出测试信息并结束业务流程
	log.Printf("🔍 [OCR-TEST] ==================== OCR深度测试模式 ====================")
	log.Printf("🔍 [OCR-TEST] OCR模型调用已完成，原始响应数据已输出到日志文件")
	log.Printf("🔍 [OCR-TEST] 模型名称: %s", s.cfg.Models.OCRModel)
	log.Printf("🔍 [OCR-TEST] 图片URL: %s", ctx.UserURL)
	log.Printf("🔍 [OCR-TEST] Token消耗: %d", ctx.OCRToken)
	log.Printf("🔍 [OCR-TEST] 题目类型: %s", saveQuestion.Type)
	log.Printf("🔍 [OCR-TEST] 题目内容长度: %d", len(saveQuestion.Content))
	log.Printf("🔍 [OCR-TEST] 选项数量: %d", len(saveQuestion.Options))
	log.Printf("🔍 [OCR-TEST] 哈希键: %s", saveQuestion.HashKey)
	log.Printf("🔍 [OCR-TEST] ==================== OCR测试完成，业务流程结束 ====================")
	log.Printf("🔍 [OCR-TEST] 请查看logs目录下的OCR响应日志文件进行深度分析")
	log.Printf("🔍 [OCR-TEST] 服务将继续运行，可以处理下一个请求")

	// 设置特殊标记，表示这是OCR测试模式，不继续后续业务逻辑
	ctx.IsOCRTest = true

	return nil
}

// checkRedisCache 检查Redis缓存
func (s *SolveService) checkRedisCache(ctx *models.ProcessContext) bool {
	questions, err := s.cacheService.GetQuestionsByHashKey(ctx.SaveQuestion.HashKey)
	if err != nil || len(questions) == 0 {
		return false
	}

	// 缓存命中
	responseData, _ := json.Marshal(questions)
	ctx.SetSuccess("redis", responseData)
	return true
}

// checkMySQLExactMatch 检查MySQL精确匹配
func (s *SolveService) checkMySQLExactMatch(ctx *models.ProcessContext) bool {
	questions, err := s.questionService.GetQuestionsByHashKey(ctx.SaveQuestion.HashKey)
	if err != nil || len(questions) == 0 {
		return false
	}

	// 转换为响应格式
	responses := s.parserService.ConvertToQuestionResponse(questions)
	responseData, _ := json.Marshal(responses)

	// 回写Redis缓存
	s.cacheService.SetQuestionCache(ctx.SaveQuestion.HashKey, responses)

	ctx.SetSuccess("mysql", responseData)
	return true
}

// checkMySQLFuzzyMatch 检查MySQL模糊匹配
func (s *SolveService) checkMySQLFuzzyMatch(ctx *models.ProcessContext) bool {
	questions, err := s.matchService.MatchQuestion(ctx.SaveQuestion)
	if err != nil || len(questions) == 0 {
		return false
	}

	// 转换为响应格式
	responses := s.parserService.ConvertToQuestionResponse(questions)
	responseData, _ := json.Marshal(responses)

	ctx.SetSuccess("match", responseData)
	return true
}

// processSolve 处理Solve模型解答
func (s *SolveService) processSolve(ctx *models.ProcessContext) error {
	// 构建题目文本
	questionText := fmt.Sprintf("题目类型：%s\n题干：%s\n选项：%v",
		ctx.SaveQuestion.Type,
		ctx.SaveQuestion.Content,
		ctx.SaveQuestion.Options,
	)

	// 调用Solve模型
	response, err := s.modelService.CallSolveModel(s.cfg.Models.SolveModel, questionText)
	if err != nil {
		return fmt.Errorf("Solve模型调用失败: %w", err)
	}

	// 解析Solve响应
	solveData, err := s.parserService.ParseSolveResponse(response)
	if err != nil {
		return err
	}

	// 更新SaveQuestion
	ctx.SaveQuestion.Answer = solveData.Answer
	ctx.SaveQuestion.Analysis = solveData.Analysis

	return nil
}

// saveAndCache 保存题目并缓存
func (s *SolveService) saveAndCache(ctx *models.ProcessContext) error {
	// 保存到数据库
	question, err := s.questionService.SaveQuestion(ctx.SaveQuestion)
	if err != nil {
		return fmt.Errorf("保存题目失败: %w", err)
	}

	// 查询所有相同hash_key的题目
	allQuestions, err := s.questionService.GetQuestionsByHashKey(ctx.SaveQuestion.HashKey)
	if err != nil {
		return err
	}

	// 转换为响应格式
	responses := s.parserService.ConvertToQuestionResponse(allQuestions)
	responseData, _ := json.Marshal(responses)

	// 回写Redis缓存
	s.cacheService.SetQuestionCache(ctx.SaveQuestion.HashKey, responses)

	ctx.SetSuccess("ai", responseData)

	// 记录匹配的题目ID
	if len(allQuestions) > 0 {
		ctx.SaveQuestion.HashKey = fmt.Sprintf("%d", question.ID)
	}

	return nil
}
