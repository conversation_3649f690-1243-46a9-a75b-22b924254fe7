package services

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"solve-go-api/internal/models"
	"strings"
	"time"

	"gorm.io/gorm"
)

// ModelService 模型服务
type ModelService struct {
	db *gorm.DB
}

// NewModelService 创建模型服务
func NewModelService(db *gorm.DB) *ModelService {
	return &ModelService{db: db}
}

// writeOCRResponseToLog 将OCR原始响应数据输出到日志文件
func (s *ModelService) writeOCRResponseToLog(modelName, imageURL string, requestBody map[string]interface{}, responseBody []byte) error {
	// 确保logs目录存在
	logsDir := "logs"
	if err := os.MkdirAll(logsDir, 0755); err != nil {
		return fmt.Errorf("failed to create logs directory: %w", err)
	}

	// 生成日志文件名，包含时间戳
	timestamp := time.Now().Format("20060102_150405")
	logFileName := fmt.Sprintf("ocr_response_%s.log", timestamp)
	logFilePath := filepath.Join(logsDir, logFileName)

	// 创建日志文件
	logFile, err := os.Create(logFilePath)
	if err != nil {
		return fmt.Errorf("failed to create log file: %w", err)
	}
	defer logFile.Close()

	// 解析响应JSON以便格式化输出
	var responseJSON interface{}
	if err := json.Unmarshal(responseBody, &responseJSON); err != nil {
		log.Printf("⚠️ [OCR-TEST] 无法解析响应JSON，使用原始字符串: %v", err)
		responseJSON = string(responseBody)
	}

	// 构建详细的日志内容
	logData := map[string]interface{}{
		"timestamp":     time.Now().Format("2006-01-02 15:04:05"),
		"model_name":    modelName,
		"image_url":     imageURL,
		"request_body":  requestBody,
		"response_raw":  string(responseBody),
		"response_json": responseJSON,
		"test_info": map[string]interface{}{
			"description": "OCR模型深度测试 - 原始响应数据分析",
			"purpose":     "用于分析OCR模型的响应格式、内容准确性和性能表现",
			"note":        "response_raw为原始字符串，response_json为解析后的结构化数据",
		},
	}

	// 将日志数据转换为格式化的JSON
	logJSON, err := json.MarshalIndent(logData, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal log data: %w", err)
	}

	// 写入日志文件
	if _, err := logFile.Write(logJSON); err != nil {
		return fmt.Errorf("failed to write log file: %w", err)
	}

	log.Printf("🔍 [OCR-TEST] OCR原始响应数据已输出到日志文件: %s", logFilePath)
	return nil
}

// GetModelByName 根据名称获取模型配置
func (s *ModelService) GetModelByName(name string) (*models.Model, error) {
	var model models.Model
	err := s.db.Where("model_name = ?", name).First(&model).Error
	if err != nil {
		return nil, fmt.Errorf("model not found: %s", name)
	}
	return &model, nil
}

// CallOCRModel 调用OCR模型
func (s *ModelService) CallOCRModel(modelName, imageURL string) (*models.OCRResponse, error) {
	model, err := s.GetModelByName(modelName)
	if err != nil {
		return nil, err
	}

	if model.ModelType != "OCR" {
		return nil, fmt.Errorf("model %s is not an OCR model", modelName)
	}

	// 构建请求体
	requestBody := map[string]interface{}{
		"model": model.ModelName,
		"input": map[string]interface{}{
			"messages": []map[string]interface{}{
				{
					"role":    "system",
					"content": model.RoleSystem,
				},
				{
					"role": "user",
					"content": []map[string]interface{}{
						{
							"image": imageURL,
						},
						{
							"text": model.RoleUser,
						},
					},
				},
			},
		},
		"parameters": map[string]interface{}{
			"temperature":        model.Temperature,
			"top_p":              model.TopP,
			"top_k":              model.TopK,
			"repetition_penalty": model.RepetitionPenalty,
			"presence_penalty":   model.PresencePenalty,
			"result_format":      model.ResponseFormat,
		},
	}

	return s.callModel(model, requestBody, modelName, imageURL)
}

// CallSolveModel 调用Solve模型
func (s *ModelService) CallSolveModel(modelName string, questionText string) (*models.SolveResponse, error) {
	model, err := s.GetModelByName(modelName)
	if err != nil {
		return nil, err
	}

	if model.ModelType != "solve" {
		return nil, fmt.Errorf("model %s is not a solve model", modelName)
	}

	var requestBody map[string]interface{}

	// 根据模型类型构建不同的请求体
	if strings.Contains(model.ModelName, "qwen") {
		// Qwen模型请求格式
		requestBody = map[string]interface{}{
			"model": model.ModelName,
			"input": map[string]interface{}{
				"messages": []map[string]interface{}{
					{
						"role":    "system",
						"content": model.RoleSystem,
					},
					{
						"role":    "user",
						"content": fmt.Sprintf("%s\n\n题目内容：%s", model.RoleUser, questionText),
					},
				},
			},
			"parameters": map[string]interface{}{
				"temperature":        model.Temperature,
				"top_p":              model.TopP,
				"top_k":              model.TopK,
				"repetition_penalty": model.RepetitionPenalty,
				"presence_penalty":   model.PresencePenalty,
				"result_format":      model.ResponseFormat,
			},
		}
	} else if strings.Contains(model.ModelName, "deepseek") {
		// DeepSeek模型请求格式
		requestBody = map[string]interface{}{
			"model": model.ModelName,
			"messages": []map[string]interface{}{
				{
					"role":    "system",
					"content": model.RoleSystem,
				},
				{
					"role":    "user",
					"content": fmt.Sprintf("%s\n\n题目内容：%s", model.RoleUser, questionText),
				},
			},
			"temperature":     model.Temperature,
			"top_p":           model.TopP,
			"max_tokens":      2048,
			"response_format": map[string]string{"type": model.ResponseFormat},
		}
	} else {
		return nil, fmt.Errorf("unsupported model type: %s", model.ModelName)
	}

	response, err := s.callModel(model, requestBody, "", "")
	if err != nil {
		return nil, err
	}

	// 🔍 调试日志：检查原始响应
	log.Printf("🔍 [SOLVE-DEBUG] 原始响应Choices数量: %d", len(response.Output.Choices))
	log.Printf("🔍 [SOLVE-DEBUG] 原始响应Usage: %+v", response.Usage)
	if len(response.Output.Choices) > 0 {
		log.Printf("🔍 [SOLVE-DEBUG] 第一个Choice内容类型: %T", response.Output.Choices[0].Message.Content)
		log.Printf("🔍 [SOLVE-DEBUG] 第一个Choice内容: %v", response.Output.Choices[0].Message.Content)
	} else {
		log.Printf("🔍 [SOLVE-DEBUG] Choices为空，可能是模型响应格式问题")
	}

	// 转换为SolveResponse
	solveResponse := &models.SolveResponse{
		Output: struct {
			Choices []struct {
				Message struct {
					Content string `json:"content"`
				} `json:"message"`
			} `json:"choices"`
		}{
			Choices: make([]struct {
				Message struct {
					Content string `json:"content"`
				} `json:"message"`
			}, len(response.Output.Choices)),
		},
		Usage: response.Usage,
	}

	// 复制choices数据，处理content类型转换
	for i, choice := range response.Output.Choices {
		var contentStr string
		switch v := choice.Message.Content.(type) {
		case string:
			contentStr = v
		default:
			// 对于solve模型，如果不是字符串，转换为JSON字符串
			if jsonBytes, err := json.Marshal(v); err == nil {
				contentStr = string(jsonBytes)
			} else {
				contentStr = fmt.Sprintf("%v", v)
			}
		}
		solveResponse.Output.Choices[i].Message.Content = contentStr
	}

	return solveResponse, nil
}

// callModel 通用模型调用方法
func (s *ModelService) callModel(model *models.Model, requestBody map[string]interface{}, modelName, imageURL string) (*models.OCRResponse, error) {
	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	// 创建HTTP请求
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	req, err := http.NewRequestWithContext(ctx, "POST", model.ModelURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+model.ModelKey)

	// 发送请求
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	// 🔍 调试日志：记录原始响应
	log.Printf("🔍 [HTTP-DEBUG] 响应状态码: %d", resp.StatusCode)
	log.Printf("🔍 [HTTP-DEBUG] 响应体长度: %d", len(body))
	if len(body) > 500 {
		log.Printf("🔍 [HTTP-DEBUG] 响应体前500字符: %s", string(body)[:500])
	} else {
		log.Printf("🔍 [HTTP-DEBUG] 完整响应体: %s", string(body))
	}

	// 解析响应
	var response models.OCRResponse
	if err := json.Unmarshal(body, &response); err != nil {
		log.Printf("🔍 [HTTP-DEBUG] JSON解析失败: %v", err)
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	// 🔧 处理Solve模型的特殊响应格式
	if len(response.Output.Choices) == 0 {
		log.Printf("🔍 [HTTP-DEBUG] Choices为空，尝试处理Solve模型格式")

		// 尝试解析为通用的map格式
		var rawResponse map[string]interface{}
		if err := json.Unmarshal(body, &rawResponse); err == nil {
			if output, ok := rawResponse["output"].(map[string]interface{}); ok {
				if text, ok := output["text"].(string); ok {
					log.Printf("🔍 [HTTP-DEBUG] 从output.text提取内容: %s", text[:min(100, len(text))])

					// 创建标准格式的Choice
					response.Output.Choices = []struct {
						Message struct {
							Content interface{} `json:"content"`
						} `json:"message"`
					}{
						{
							Message: struct {
								Content interface{} `json:"content"`
							}{
								Content: text,
							},
						},
					}
					log.Printf("🔍 [HTTP-DEBUG] 成功转换为标准格式")
				}
			}
		}
	}

	// 🔍 [OCR-TEST] 如果是OCR模型调用，输出原始响应数据到日志文件
	if modelName != "" && imageURL != "" {
		log.Printf("🔍 [OCR-TEST] 检测到OCR模型调用，开始输出原始响应数据到日志文件")
		if err := s.writeOCRResponseToLog(modelName, imageURL, requestBody, body); err != nil {
			log.Printf("❌ [OCR-TEST] 输出OCR响应日志失败: %v", err)
		} else {
			log.Printf("✅ [OCR-TEST] OCR响应日志输出成功")
		}
	}

	return &response, nil
}
