# OCR深度测试功能说明

## 概述

本次修改针对拍照搜题业务中的OCR模型调用进行了深度测试功能的添加。修改后的系统将在OCR模型调用完成后，输出原始响应数据到日志文件，并立即中断进程，便于对OCR模型的响应进行详细分析。

## 修改内容

### 1. 文件修改列表

- `internal/services/model_service.go` - 添加OCR响应日志输出功能
- `internal/services/solve_service.go` - 添加OCR处理完成后的进程中断逻辑

### 2. 主要功能

#### 2.1 OCR响应日志输出 (`model_service.go`)

- **新增函数**: `writeOCRResponseToLog()`
  - 将OCR模型的原始响应数据输出到日志文件
  - 日志文件位置: `logs/ocr_response_YYYYMMDD_HHMMSS.log`
  - 包含完整的请求和响应数据

- **修改函数**: `callModel()`
  - 增加了modelName和imageURL参数
  - 在OCR模型调用时自动输出响应日志

#### 2.2 进程中断逻辑 (`solve_service.go`)

- **修改函数**: `processOCR()`
  - 在OCR处理完成后添加详细的测试信息输出
  - 使用`os.Exit(0)`中断进程，防止继续执行后续业务逻辑

### 3. 日志文件格式

OCR响应日志文件包含以下信息：
```json
{
  "timestamp": "2006-01-02 15:04:05",
  "model_name": "qwen-vl-plus",
  "image_url": "https://example.com/image.jpg",
  "request_body": {
    // 完整的请求体数据
  },
  "response_raw": "原始响应JSON字符串"
}
```

## 使用方法

### 1. 编译程序
```bash
go build -o solve-api main.go
```

### 2. 运行测试

#### 方法一：使用快速测试脚本
```bash
./quick_ocr_test.sh
```

#### 方法二：使用完整测试脚本
```bash
./test_ocr_deep_analysis.sh
```

#### 方法三：手动测试
```bash
# 启动服务
./solve-api &

# 发送测试请求
curl -X POST http://localhost:8080/api/v1/solve/question \
  -H "Content-Type: application/json" \
  -d '{
    "app_id": "PvpKuwyCBxUmnvBG",
    "app_secret": "FNKdsTcZSHrKJIMCtK3sDbfOQHBGnbFN",
    "image_url": "https://example.com/test-image.jpg"
  }'
```

### 3. 查看结果

测试完成后，检查以下内容：

1. **控制台输出**: 查看OCR测试相关的日志信息
2. **OCR响应日志**: 查看`logs/ocr_response_*.log`文件
3. **服务状态**: 确认服务在OCR处理完成后自动中断

## 预期行为

1. **正常流程**:
   - 服务启动
   - 接收API请求
   - 验证应用和用户
   - 检查用户积分
   - 调用OCR模型
   - 输出OCR原始响应到日志文件
   - 显示详细的测试信息
   - 自动中断进程

2. **日志输出**:
   - 控制台显示详细的OCR测试信息
   - `logs/`目录下生成OCR响应日志文件
   - 日志文件包含完整的请求和响应数据

3. **进程行为**:
   - 在OCR处理完成后立即中断
   - 不会继续执行Redis缓存检查、数据库查询等后续逻辑

## 注意事项

1. **测试环境**: 此修改仅用于OCR模型的深度测试，不适用于生产环境
2. **数据安全**: 日志文件可能包含敏感信息，请妥善保管
3. **进程中断**: 服务会在OCR处理完成后自动中断，这是预期行为
4. **日志清理**: 建议定期清理OCR响应日志文件，避免占用过多磁盘空间

## 恢复正常功能

如需恢复正常的业务逻辑（不中断进程），请：

1. 注释或删除`solve_service.go`中`processOCR()`函数末尾的测试代码
2. 移除`os.Exit(0)`调用
3. 重新编译程序

## 故障排除

1. **编译错误**: 检查Go环境和依赖包
2. **服务启动失败**: 检查配置文件和数据库连接
3. **OCR调用失败**: 检查OCR模型配置和网络连接
4. **日志文件未生成**: 检查logs目录权限和磁盘空间
